import { ImageBucket } from "ui/src/utils";
import { NextResponse } from "next/server";
import { prisma } from "prisma-db";

export async function POST(req: Request) {
  const formData = await req.formData();

  const { searchParams } = new URL(req.url);
  const key = searchParams.get("key");
  const spaceId = searchParams.get("spaceId");
  const articleId = searchParams.get("articleId");
  const mediaType = searchParams.get("mediaType") || "images";

  const file = formData.get("file") as File;

  if (!file) {
    return NextResponse.json({ error: "No files received." }, { status: 400 });
  }

  if (!spaceId) {
    return NextResponse.json(
      { error: "spaceId is required." },
      { status: 400 },
    );
  }

  if (!articleId) {
    return NextResponse.json(
      { error: "articleId is required." },
      { status: 400 },
    );
  }

  if (!file.type.match(/(jpg|jpeg|png|webp)$/i)) {
    return NextResponse.json(
      { error: "Only jpg,png,webp formats are allowed!" },
      { status: 400 },
    );
  }

  const fileName = key || file.name;
  const fileKey = `spaces/${spaceId}/articles/${articleId}/${mediaType}/${fileName}`;

  try {
    // Use environment variables for S3 configuration
    const bucketName = process.env.S3_BUCKET_NAME;
    const region = process.env.AWS_REGION;

    if (!bucketName || !region) {
      return NextResponse.json(
        { error: "S3 configuration not found in environment variables" },
        { status: 500 },
      );
    }

    const bucket = new ImageBucket();

    // Upload with 10MB size limit
    await bucket.uploadFile({
      key: fileKey,
      file,
      maxSizeBytes: 10 * 1024 * 1024,
    });

    const fileUrl = `https://${bucketName}.s3.${region}.amazonaws.com/${fileKey}`;

    // Create Asset record in database
    const asset = await prisma.asset.create({
      data: {
        spaceId,
        articleId: articleId === "temp" ? null : articleId,
        mediaType,
        fileName,
        fileKey,
        url: fileUrl,
        fileSize: file.size,
        mimeType: file.type,
      },
    });

    return NextResponse.json({
      message: "File uploaded successfully.",
      url: fileUrl,
      asset: {
        id: asset.id,
        fileName: asset.fileName,
        fileSize: asset.fileSize,
        mimeType: asset.mimeType,
      },
    });
  } catch (error) {
    console.error("Upload error:", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Upload failed" },
      { status: 400 },
    );
  }
}

import { auth } from "@/auth";
import { prisma } from "prisma-db";
import { z } from "zod";

const configSchema = z.object({
  authGoogleId: z.string().optional(),
  authGoogleSecret: z.string().optional(),
  gaMeasurementId: z.string().optional(),
  openaiApiKey: z.string().optional(),
  googleApiKey: z.string().optional(),
  nextPublicStripePublishableKey: z.string().optional(),
  stripeSecretKey: z.string().optional(),
  stripeWebhookSecret: z.string().optional(),
  nextPublicFaviconUrl: z.string().optional(),
});

export type ConfigData = z.infer<typeof configSchema>;

// GET - Get space configuration (admin only)
export async function GET(
  req: Request,
  { params }: { params: Promise<{ spaceId: string }> },
) {
  const res = await auth();
  if (!res?.user || res.user.role !== "ADMIN") {
    return Response.json({ message: "Unauthorized" }, { status: 401 });
  }

  const { spaceId } = await params;

  try {
    const space = await prisma.space.findUnique({
      where: { id: spaceId },
      select: {
        id: true,
        title: true,
        description: true,
        authGoogleId: true,
        authGoogleSecret: true,
        gaMeasurementId: true,
        openaiApiKey: true,
        googleApiKey: true,
        nextPublicStripePublishableKey: true,
        stripeSecretKey: true,
        stripeWebhookSecret: true,
        nextPublicFaviconUrl: true,
      },
    });

    if (!space) {
      return Response.json({ message: "Space not found" }, { status: 404 });
    }

    // Mask sensitive values for security
    const maskedConfig = {
      ...space,
      authGoogleSecret: space.authGoogleSecret ? "••••••••" : "",
      openaiApiKey: space.openaiApiKey ? "••••••••" : "",
      googleApiKey: space.googleApiKey ? "••••••••" : "",
      stripeSecretKey: space.stripeSecretKey ? "••••••••" : "",
      stripeWebhookSecret: space.stripeWebhookSecret ? "••••••••" : "",
    };

    return Response.json(maskedConfig);
  } catch (error) {
    return Response.json(
      {
        message: "Failed to fetch configuration",
        error,
      },
      { status: 500 },
    );
  }
}

// PUT - Update space configuration (admin only)
export async function PUT(
  req: Request,
  { params }: { params: Promise<{ spaceId: string }> },
) {
  const res = await auth();
  if (!res?.user || res.user.role !== "ADMIN") {
    return Response.json({ message: "Unauthorized" }, { status: 401 });
  }

  const { spaceId } = await params;

  try {
    const data = await req.json();
    const validatedData = configSchema.parse(data);

    // Remove empty strings and convert to null
    const cleanedData = Object.fromEntries(
      Object.entries(validatedData).map(([key, value]) => [
        key,
        value === "" ? null : value,
      ]),
    );

    // Only update fields that are not masked (not "••••••••")
    const updateData: Partial<ConfigData> = {};
    for (const [key, value] of Object.entries(cleanedData)) {
      if (value !== "••••••••") {
        updateData[key as keyof ConfigData] = value || undefined;
      }
    }

    const updatedSpace = await prisma.space.update({
      where: { id: spaceId },
      data: updateData,
      select: {
        id: true,
        title: true,
        description: true,
        authGoogleId: true,
        authGoogleSecret: true,
        gaMeasurementId: true,
        openaiApiKey: true,
        googleApiKey: true,
        nextPublicStripePublishableKey: true,
        stripeSecretKey: true,
        stripeWebhookSecret: true,
        nextPublicFaviconUrl: true,
      },
    });

    // Mask sensitive values for response
    const maskedConfig = {
      ...updatedSpace,
      authGoogleSecret: updatedSpace.authGoogleSecret ? "••••••••" : "",
      openaiApiKey: updatedSpace.openaiApiKey ? "••••••••" : "",
      googleApiKey: updatedSpace.googleApiKey ? "••••••••" : "",
      stripeSecretKey: updatedSpace.stripeSecretKey ? "••••••••" : "",
      stripeWebhookSecret: updatedSpace.stripeWebhookSecret ? "••••••••" : "",
    };

    return Response.json(maskedConfig);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return Response.json(
        {
          message: "Invalid configuration data",
          errors: error.errors,
        },
        { status: 400 },
      );
    }

    return Response.json(
      {
        message: "Failed to update configuration",
        error,
      },
      { status: 500 },
    );
  }
}

import { NextResponse } from "next/server";
import { prisma } from "prisma-db";
import { auth } from "@/auth";

export async function GET(req: Request) {
  const res = await auth();
  if (!res?.user || res.user.role !== "ADMIN") {
    return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
  }

  const { searchParams } = new URL(req.url);
  const spaceId = searchParams.get("spaceId");
  const articleId = searchParams.get("articleId");

  try {
    const assets = await prisma.asset.findMany({
      where: {
        ...(spaceId && { spaceId }),
        ...(articleId && { articleId }),
      },
      orderBy: {
        createdAt: "desc",
      },
      include: {
        space: {
          select: {
            id: true,
            title: true,
          },
        },
        article: {
          select: {
            id: true,
            slug: true,
          },
        },
      },
    });

    return NextResponse.json(assets);
  } catch (error) {
    console.error("Error fetching assets:", error);
    return NextResponse.json(
      { error: "Failed to fetch assets" },
      { status: 500 },
    );
  }
}

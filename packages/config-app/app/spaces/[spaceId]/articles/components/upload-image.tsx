import React, { useState, useRef } from "react";
import { Loader2, Plus } from "lucide-react";
import { Input } from "@/components/ui/input";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import { ImageResize } from "ui/src/utils/image-resize";

const validateAndResizeFile = async (
  file: File,
  toast: ReturnType<typeof useToast>["toast"],
) => {
  const isImage = ["image/jpeg", "image/png", "image/webp"].includes(file.type);
  if (!isImage) {
    toast({
      title: "Invalid file type",
      description: "You can only upload JPG/PNG/WEBP files!",
      variant: "destructive",
    });
    return null;
  }

  // Check file size (10MB limit)
  if (file.size > 10 * 1024 * 1024) {
    toast({
      title: "File too large",
      description: "File size must be less than 10MB!",
      variant: "destructive",
    });
    return null;
  }

  const imageResize = new ImageResize({ width: 1200 });
  const resizedFile = await imageResize.resizeFile(file);

  if (!resizedFile) {
    toast({
      title: "Processing failed",
      description: "Something went wrong with your image!",
      variant: "destructive",
    });
    return null;
  }
  return resizedFile;
};

export const UploadImage = ({
  value,
  onChange,
  spaceId,
  articleId,
  mediaType = "images",
}: {
  value?: string;
  onChange?: (value: string) => void;
  spaceId?: string;
  articleId?: string;
  mediaType?: string;
}) => {
  const [loading, setLoading] = useState(false);
  const [imageUrl, setImageUrl] = useState<string>(value ?? "");
  const [mode, setMode] = useState<"upload" | "edit">("upload");
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  const handleFileUpload = async (file: File) => {
    if (!spaceId) {
      toast({
        title: "Error",
        description: "spaceId is required for file upload",
        variant: "destructive",
      });
      return;
    }

    // Use articleId if available, otherwise use 'temp' for new articles
    const effectiveArticleId = articleId || "temp";

    setLoading(true);
    try {
      const resizedFile = await validateAndResizeFile(file, toast);
      if (!resizedFile) {
        setLoading(false);
        return;
      }

      const formData = new FormData();
      formData.append(
        "file",
        new File([resizedFile], file.name, { type: resizedFile.type }),
      );

      // Build URL with required parameters
      const uploadUrl = new URL(
        "/api/manage/image-upload",
        window.location.origin,
      );
      if (spaceId) {
        uploadUrl.searchParams.set("spaceId", spaceId);
      }
      if (effectiveArticleId) {
        uploadUrl.searchParams.set("articleId", effectiveArticleId);
      }
      if (mediaType) uploadUrl.searchParams.set("mediaType", mediaType);

      const response = await fetch(uploadUrl.toString(), {
        method: "POST",
        body: formData,
      });

      const data = await response.json();
      if (response.ok) {
        const newImageUrl = data.url;
        onChange?.(newImageUrl);
        setImageUrl(newImageUrl + "?v=" + Date.now());
        toast({
          title: "Success",
          description: "Image uploaded successfully!",
        });
      } else {
        throw new Error(data.error || "Upload failed");
      }
    } catch (error) {
      toast({
        title: "Upload failed",
        description:
          error instanceof Error ? error.message : "Something went wrong",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      handleFileUpload(file);
    }
  };

  const renderContent = () => {
    if (mode === "edit") {
      return (
        <div className="flex flex-col">
          <div className="relative border border-dashed border-gray-300 rounded-xl flex items-center justify-center flex-col w-[270px] h-[135px] overflow-hidden">
            <Input
              className="w-full h-full resize-none border-0 focus-visible:ring-0"
              value={value}
              onChange={(e) => {
                const imageUrl = e.target.value;
                onChange?.(imageUrl);
                setImageUrl(imageUrl + "?v=" + Date.now());
              }}
              placeholder="Enter image URL"
            />
          </div>
        </div>
      );
    }

    const displayImage = imageUrl || value;

    return (
      <div className="flex flex-col">
        <input
          type="file"
          ref={fileInputRef}
          onChange={handleFileChange}
          accept="image/jpeg,image/png,image/webp"
          className="hidden"
        />
        <div
          className="border border-dashed border-gray-300 rounded-xl flex items-center justify-center flex-col w-[270px] h-[135px] overflow-hidden cursor-pointer hover:border-gray-400 transition-colors"
          onClick={() => fileInputRef.current?.click()}
        >
          {displayImage && !loading ? (
            // eslint-disable-next-line @next/next/no-img-element
            <img
              src={displayImage}
              alt="banner"
              className="w-full h-full object-cover"
            />
          ) : (
            <>
              {loading ? (
                <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
              ) : (
                <Plus className="h-8 w-8 text-gray-400" />
              )}
              <div className="mt-2 text-sm text-gray-500">
                {loading ? "Uploading..." : "Upload"}
              </div>
            </>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="group relative">
      {renderContent()}
      <div className="absolute bottom-2 right-2 hidden group-hover:block">
        <RadioGroup
          value={mode}
          onValueChange={(value) => setMode(value as "upload" | "edit")}
          className="flex space-x-2"
        >
          <div className="flex items-center space-x-1">
            <RadioGroupItem value="upload" id="upload" className="sr-only" />
            <Label
              htmlFor="upload"
              className={`px-2 py-1 text-xs rounded cursor-pointer ${
                mode === "upload"
                  ? "bg-primary text-primary-foreground"
                  : "bg-secondary"
              }`}
            >
              Upload
            </Label>
          </div>
          <div className="flex items-center space-x-1">
            <RadioGroupItem value="edit" id="edit" className="sr-only" />
            <Label
              htmlFor="edit"
              className={`px-2 py-1 text-xs rounded cursor-pointer ${
                mode === "edit"
                  ? "bg-primary text-primary-foreground"
                  : "bg-secondary"
              }`}
            >
              Edit
            </Label>
          </div>
        </RadioGroup>
      </div>
    </div>
  );
};

export default UploadImage;

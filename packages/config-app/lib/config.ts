import { prisma } from "prisma-db";

// Cache for configuration to avoid repeated database queries
let configCache: SpaceConfig | null = null;
let cacheTimestamp: number = 0;
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes

export interface SpaceConfig {
  authGoogleId?: string | null;
  authGoogleSecret?: string | null;
  gaMeasurementId?: string | null;
  openaiApiKey?: string | null;
  googleApiKey?: string | null;
  nextPublicStripePublishableKey?: string | null;
  stripeSecretKey?: string | null;
  stripeWebhookSecret?: string | null;
  nextPublicFaviconUrl?: string | null;
}

/**
 * Get configuration from Space model with fallback to environment variables
 */
export async function getConfig(): Promise<SpaceConfig> {
  const now = Date.now();

  // Return cached config if still valid
  if (configCache && now - cacheTimestamp < CACHE_TTL) {
    return configCache;
  }

  const spaceId = process.env.SPACE_ID;

  if (!spaceId) {
    // If no SPACE_ID, return environment variables
    return getEnvConfig();
  }

  try {
    const space = await prisma.space.findFirst({
      where: { id: spaceId },
      select: {
        authGoogleId: true,
        authGoogleSecret: true,
        s3BucketName: true,
        awsRegion: true,
        awsAccessKeyId: true,
        awsSecretAccessKey: true,
        gaMeasurementId: true,
        openaiApiKey: true,
        googleApiKey: true,
        nextPublicStripePublishableKey: true,
        stripeSecretKey: true,
        stripeWebhookSecret: true,
        nextPublicFaviconUrl: true,
      },
    });

    if (!space) {
      // If space not found, fallback to environment variables
      return getEnvConfig();
    }

    // Merge space config with environment variables (space config takes precedence)
    const config = {
      authGoogleId: space.authGoogleId || process.env.AUTH_GOOGLE_ID,
      authGoogleSecret:
        space.authGoogleSecret || process.env.AUTH_GOOGLE_SECRET,
      gaMeasurementId: space.gaMeasurementId || process.env.GA_MEASUREMENT_ID,
      openaiApiKey: space.openaiApiKey || process.env.OPENAI_API_KEY,
      googleApiKey: space.googleApiKey || process.env.GOOGLE_API_KEY,
      nextPublicStripePublishableKey:
        space.nextPublicStripePublishableKey ||
        process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY,
      stripeSecretKey: space.stripeSecretKey || process.env.STRIPE_SECRET_KEY,
      stripeWebhookSecret:
        space.stripeWebhookSecret || process.env.STRIPE_WEBHOOK_SECRET,
      nextPublicFaviconUrl:
        space.nextPublicFaviconUrl || process.env.NEXT_PUBLIC_FAVICON_URL,
    };

    // Cache the result
    configCache = config;
    cacheTimestamp = now;

    return config;
  } catch (error) {
    console.error(
      "Failed to fetch config from database, falling back to environment variables:",
      error,
    );
    return getEnvConfig();
  }
}

/**
 * Get configuration from environment variables only
 */
function getEnvConfig(): SpaceConfig {
  return {
    authGoogleId: process.env.AUTH_GOOGLE_ID,
    authGoogleSecret: process.env.AUTH_GOOGLE_SECRET,
    gaMeasurementId: process.env.GA_MEASUREMENT_ID,
    openaiApiKey: process.env.OPENAI_API_KEY,
    googleApiKey: process.env.GOOGLE_API_KEY,
    nextPublicStripePublishableKey:
      process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY,
    stripeSecretKey: process.env.STRIPE_SECRET_KEY,
    stripeWebhookSecret: process.env.STRIPE_WEBHOOK_SECRET,
    nextPublicFaviconUrl: process.env.NEXT_PUBLIC_FAVICON_URL,
  };
}

/**
 * Clear the configuration cache (useful for testing or when config is updated)
 */
export function clearConfigCache(): void {
  configCache = null;
  cacheTimestamp = 0;
}

/**
 * Get a specific configuration value with fallback
 */
export async function getConfigValue<K extends keyof SpaceConfig>(
  key: K,
): Promise<SpaceConfig[K]> {
  const config = await getConfig();
  return config[key];
}

// Convenience functions for commonly used config values
export const getOpenAIApiKey = () => getConfigValue("openaiApiKey");
export const getGoogleApiKey = () => getConfigValue("googleApiKey");
export const getStripeSecretKey = () => getConfigValue("stripeSecretKey");
export const getStripeWebhookSecret = () =>
  getConfigValue("stripeWebhookSecret");
export const getNextPublicStripePublishableKey = () =>
  getConfigValue("nextPublicStripePublishableKey");
export const getAuthGoogleId = () => getConfigValue("authGoogleId");
export const getAuthGoogleSecret = () => getConfigValue("authGoogleSecret");
export const getGaMeasurementId = () => getConfigValue("gaMeasurementId");
export const getNextPublicFaviconUrl = () =>
  getConfigValue("nextPublicFaviconUrl");

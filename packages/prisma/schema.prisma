generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                   String          @id @default(cuid())
  name                 String?
  email                String          @unique
  emailVerified        DateTime?
  image                String?
  role                 Role            @default(USER)
  createdAt            DateTime        @default(now())
  updatedAt            DateTime        @updatedAt
  subscriptionStatus   Boolean         @default(false)
  stripeSubscriptionId String?
  stripeCustomerId     String?
  accounts             Account[]
  Authenticator        Authenticator[]
  sessions             Session[]
  spaces               Space[]
}

model Account {
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  user              User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@id([provider, providerAccountId])
}

model Session {
  sessionToken String   @unique
  userId       String
  expires      DateTime
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model VerificationToken {
  identifier String
  token      String
  expires    DateTime

  @@id([identifier, token])
}

model Authenticator {
  credentialID         String  @unique
  userId               String
  providerAccountId    String
  credentialPublicKey  String
  counter              Int
  credentialDeviceType String
  credentialBackedUp   Boolean
  transports           String?
  user                 User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@id([userId, credentialID])
}

enum Role {
  ADMIN
  AUTHOR
  USER
}

model Space {
  id          String     @id @default(cuid())
  title       String
  description String
  ownerId     String
  articles    Article[]
  assets      Asset[]
  owner       User       @relation(fields: [ownerId], references: [id], onDelete: Cascade)
  Category    Category[]

  // Configuration fields
  authGoogleId                    String?
  authGoogleSecret                String?
  s3BucketName                    String?
  awsRegion                       String?
  awsAccessKeyId                  String?
  awsSecretAccessKey              String?
  gaMeasurementId                 String?
  openaiApiKey                    String?
  googleApiKey                    String?
  nextPublicStripePublishableKey  String?
  stripeSecretKey                 String?
  stripeWebhookSecret             String?
  nextPublicFaviconUrl            String?
}

model Article {
  id          String           @id @default(cuid())
  createdAt   DateTime         @default(now())
  updatedAt   DateTime         @updatedAt
  isPremium   Boolean
  isPublished Boolean
  articleDate DateTime
  slug        String
  spaceId     String
  Space       Space            @relation(fields: [spaceId], references: [id], onDelete: Cascade)
  contents    ArticleContent[]
  assets      Asset[]
  tags        String[]
  categories  Category[]
}

model ArticleContent {
  id             String   @id @default(cuid())
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt
  isReady        Boolean  @default(false)
  title          String
  imageUrl       String
  seoDescription String
  content        String
  language       String
  articleId      String?
  Article        Article? @relation(fields: [articleId], references: [id], onDelete: Cascade)
}

model Category {
  id       String          @id @default(cuid())
  spaceId  String
  Space    Space           @relation(fields: [spaceId], references: [id], onDelete: Cascade)
  articles Article[]
  labels   CategoryLabel[]
}

model CategoryLabel {
  id         String    @id @default(cuid())
  Category   Category? @relation(fields: [categoryId], references: [id], onDelete: Cascade)
  categoryId String?
  label      String
  language   String
}

model Asset {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  spaceId   String
  articleId String?
  mediaType String   // e.g., "images", "videos", "documents"
  fileName  String
  fileKey   String   @unique // The S3 key: [spaceId]/[articleId]/[mediaType]/[fileName]
  url       String
  fileSize  Int      // File size in bytes
  mimeType  String   // MIME type of the file
  space     Space    @relation(fields: [spaceId], references: [id], onDelete: Cascade)
  article   Article? @relation(fields: [articleId], references: [id], onDelete: Cascade)

  @@index([spaceId])
  @@index([articleId])
  @@index([mediaType])
}
